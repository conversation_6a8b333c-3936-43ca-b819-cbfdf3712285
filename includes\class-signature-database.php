<?php
/**
 * 数据库操作类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 数据库操作类
 */
class WP_Esig_Database {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        // 初始化数据库操作
    }
    
    // 合同模板相关方法已移除 - 改为文件方式
    
    /**
     * 保存签名记录
     */
    public function save_signature($data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_signatures';

        // 基础数据
        $insert_data = array(
            'order_id' => isset($data['order_id']) ? $data['order_id'] : 0,
            'user_id' => isset($data['user_id']) ? $data['user_id'] : 0,
            'signature_data' => isset($data['signature_data']) ? $data['signature_data'] : '',
            'signature_image' => isset($data['signature_image']) ? $data['signature_image'] : '',
            'signed_at' => current_time('mysql'),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
            'status' => 'completed'
        );

        $format_array = array('%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s');

        // 扩展字段（3.5.3版本新增）
        if (isset($data['pdf_filename'])) {
            $insert_data['pdf_filename'] = $data['pdf_filename'];
            $format_array[] = '%s';
        }

        if (isset($data['contract_number'])) {
            $insert_data['contract_number'] = $data['contract_number'];
            $format_array[] = '%s';
        }

        if (isset($data['customer_email'])) {
            $insert_data['customer_email'] = $data['customer_email'];
            $format_array[] = '%s';
        }

        if (isset($data['customer_cpf'])) {
            $insert_data['customer_cpf'] = $data['customer_cpf'];
            $format_array[] = '%s';
        }

        if (isset($data['customer_phone'])) {
            $insert_data['customer_phone'] = $data['customer_phone'];
            $format_array[] = '%s';
        }

        $result = $wpdb->insert($table_name, $insert_data, $format_array);

        return $result ? $wpdb->insert_id : false;
    }
    
    /**
     * 获取客户端IP地址
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * 获取签名记录
     */
    public function get_signature_by_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_signatures';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE order_id = %d ORDER BY created_at DESC LIMIT 1",
            $order_id
        ));
    }

    /**
     * 根据ID获取签名记录
     */
    public function get_signature_by_id($signature_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_signatures';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $signature_id
        ));
    }
    
    /**
     * 获取所有签名记录
     */
    public function get_signatures($limit = 20, $offset = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'esig_signatures';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
            $limit,
            $offset
        ));
    }
    
    /**
     * 获取签名记录总数
     */
    public function get_signatures_count() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_signatures';

        return $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    }

    /**
     * 删除签名记录
     */
    public function delete_signature($signature_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_signatures';

        $result = $wpdb->delete(
            $table_name,
            array('id' => $signature_id),
            array('%d')
        );

        return $result !== false;
    }

    // 所有合同模板相关方法已移除 - 改为文件方式
    // 保留签名记录相关方法
}
