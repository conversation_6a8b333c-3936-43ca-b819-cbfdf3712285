<?php
/**
 * Plugin Name: WP Electronic Signature
 * Plugin URI: https://your-website.com/wp-electronic-signature
 * Description: WordPress电子签名插件，在WooCommerce结账时集成电子签名功能，自动生成PDF合同并发送邮件。
 * Version: 3.5.2
 * Author: jiang
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wp-electronic-signature
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.8
 * Requires PHP: 8.2
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * Requires Plugins: woocommerce
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('WP_ESIG_VERSION', '3.5.2');
define('WP_ESIG_PLUGIN_FILE', __FILE__);
define('WP_ESIG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_ESIG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_ESIG_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * 主插件类
 */
class WP_Electronic_Signature {

    /**
     * 单例实例
     */
    private static $instance = null;

    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 插件激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // 插件加载完成后初始化
        add_action('plugins_loaded', array($this, 'init'), 10);

        // 声明WooCommerce HPOS兼容性
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
    }

    /**
     * 插件激活
     */
    public function activate() {
        // 检查WooCommerce是否激活
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('此插件需要WooCommerce插件才能正常工作。', 'wp-electronic-signature'));
        }

        // 创建数据库表
        $this->create_tables();

        // 设置默认选项
        $this->set_default_options();

        // 确保默认模板存在 - 已改为文件方式，跳过数据库模板创建
        // $this->ensure_default_template();

        // 刷新重写规则
        flush_rewrite_rules();
    }

    /**
     * 插件停用
     */
    public function deactivate() {
        // 清理临时数据
        $this->cleanup_temp_data();

        // 刷新重写规则
        flush_rewrite_rules();
    }

    /**
     * 初始化插件
     */
    public function init() {
        // 检查WooCommerce是否激活
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // 检查版本更新
        $this->check_version_update();

        // 加载文本域
        $this->load_textdomain();

        // 加载核心文件
        $this->load_includes();

        // 初始化核心功能
        $this->init_core();

        // 自动同步功能已禁用，避免覆盖用户自定义内容
        // $this->auto_sync_template_if_needed();
    }

    /**
     * 加载文本域
     */
    private function load_textdomain() {
        load_plugin_textdomain('wp-electronic-signature', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * 声明WooCommerce HPOS兼容性
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
        }
    }

    /**
     * 加载核心文件
     */
    private function load_includes() {
        // 核心功能类
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-core.php';
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-admin.php';
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-frontend.php';
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-database.php';
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-pdf.php';
        require_once WP_ESIG_PLUGIN_DIR . 'includes/class-signature-email.php';
    }

    /**
     * 初始化核心功能
     */
    private function init_core() {
        // 初始化数据库类
        WP_Esig_Database::get_instance();

        // 初始化核心功能
        WP_Esig_Core::get_instance();

        // 初始化邮件功能
        WP_Esig_Email::get_instance();

        // 初始化后台管理
        if (is_admin()) {
            WP_Esig_Admin::get_instance();
        }

        // 初始化前端功能
        if (!is_admin()) {
            WP_Esig_Frontend::get_instance();
        }
    }

    /**
     * 创建数据库表
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // 签名记录表
        $signatures_table = $wpdb->prefix . 'esig_signatures';
        $signatures_sql = "CREATE TABLE $signatures_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            signature_data longtext NOT NULL,
            signature_image varchar(255) NOT NULL,
            signed_at datetime NOT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text NOT NULL,
            status varchar(20) DEFAULT 'completed',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY user_id (user_id),
            KEY signed_at (signed_at)
        ) $charset_collate;";

        // 合同模板表
        $templates_table = $wpdb->prefix . 'esig_contract_templates';
        $templates_sql = "CREATE TABLE $templates_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            content longtext NOT NULL,
            variables text,
            content_hash varchar(32),
            is_default tinyint(1) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY name (name),
            KEY is_default (is_default),
            KEY content_hash (content_hash)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($signatures_sql);
        dbDelta($templates_sql);

        // 创建默认合同模板
        $this->create_default_template();

        // 更新数据库版本
        update_option('wp_esig_db_version', WP_ESIG_VERSION);
    }

    /**
     * 设置默认选项
     */
    private function set_default_options() {
        $default_options = array(
            'wp_esig_enabled' => 'yes',
            'wp_esig_signature_timing' => 'on_page_load',
            'wp_esig_signature_page_title' => __('请签署合同', 'wp-electronic-signature'),
            'wp_esig_signature_required' => 'yes',
            'wp_esig_contract_template_id' => 1,
            // SMTP邮件设置默认值
            'wp_esig_smtp_enabled' => 'no',
            'wp_esig_smtp_host' => '',
            'wp_esig_smtp_port' => '587',
            'wp_esig_smtp_username' => '',
            'wp_esig_smtp_password' => '',
            'wp_esig_smtp_encryption' => 'tls',
            'wp_esig_smtp_from_name' => get_bloginfo('name'),
            'wp_esig_seller_email' => get_option('admin_email'),
            'wp_esig_send_to_customer' => 'yes',
            'wp_esig_send_to_seller' => 'yes'
        );

        foreach ($default_options as $option_name => $option_value) {
            if (get_option($option_name) === false) {
                add_option($option_name, $option_value);
            }
        }
    }

    /**
     * 创建默认合同模板
     */
    private function create_default_template() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';
        $default_content = $this->get_default_contract_template();

        error_log('WP Electronic Signature: 开始创建默认合同模板');

        // 检查是否已存在默认模板
        $existing = $wpdb->get_row("SELECT * FROM $table_name WHERE is_default = 1");

        if ($existing) {
            error_log('WP Electronic Signature: 更新现有默认模板，ID: ' . $existing->id);
            // 更新现有默认模板
            $result = $wpdb->update(
                $table_name,
                array(
                    'name' => __('默认合同模板', 'wp-electronic-signature'),
                    'content' => $default_content,
                    'content_hash' => $this->get_default_template_hash(),
                    'status' => 'active',
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $existing->id),
                array('%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );

            if ($result !== false) {
                error_log('WP Electronic Signature: 默认模板更新成功');
            } else {
                error_log('WP Electronic Signature: 默认模板更新失败: ' . $wpdb->last_error);
            }
        } else {
            error_log('WP Electronic Signature: 创建新的默认模板');
            // 创建新的默认模板
            $result = $wpdb->insert(
                $table_name,
                array(
                    'name' => __('默认合同模板', 'wp-electronic-signature'),
                    'content' => $default_content,
                    'content_hash' => $this->get_default_template_hash(),
                    'is_default' => 1,
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ),
                array('%s', '%s', '%s', '%d', '%s', '%s', '%s')
            );

            if ($result) {
                error_log('WP Electronic Signature: 默认模板创建成功，ID: ' . $wpdb->insert_id);
            } else {
                error_log('WP Electronic Signature: 默认模板创建失败: ' . $wpdb->last_error);
            }
        }
    }

    /**
     * 确保默认模板存在
     */
    private function ensure_default_template() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';

        // 检查表是否存在
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            error_log('WP Electronic Signature: 合同模板表不存在，重新创建数据库表');
            $this->create_tables();
            return;
        }

        // 检查是否有默认模板
        $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE is_default = 1 AND status = 'active'");

        if ($template_count == 0) {
            error_log('WP Electronic Signature: 没有找到默认模板，创建新的默认模板');
            $this->create_default_template();
        } else {
            error_log('WP Electronic Signature: 找到 ' . $template_count . ' 个默认模板');
        }
    }

    /**
     * 获取默认合同模板内容
     */
    /**
     * 获取默认合同模板内容哈希
     */
    public function get_default_template_hash() {
        return md5($this->get_default_contract_template());
    }

    public function get_default_contract_template() {
        $template_file = WP_ESIG_PLUGIN_DIR . 'templates/default-contract.html';

        if (file_exists($template_file)) {
            return file_get_contents($template_file);
        }

        // 如果文件不存在，返回一个基本的模板
        error_log('WP Electronic Signature: 默认模板文件不存在: ' . $template_file);
        return '<div style="padding: 40px; text-align: center;"><h1>合同模板</h1><p>默认模板文件缺失，请联系管理员。</p></div>';
    }

    /**
     * 清理临时数据
     */
    private function cleanup_temp_data() {
        // 清理会话数据
        if (class_exists('WC') && WC()->session) {
            WC()->session->__unset('wp_esig_signature_data');
            WC()->session->__unset('wp_esig_pending_order_id');
        }

        // 清理瞬态数据
        delete_transient('wp_esig_temp_data');
    }

    /**
     * 检查版本更新
     */
    private function check_version_update() {
        $current_version = get_option('wp_esig_version', '1.0.0');

        if (version_compare($current_version, WP_ESIG_VERSION, '<')) {
            // 需要更新
            $this->update_plugin($current_version);

            // 更新版本号
            update_option('wp_esig_version', WP_ESIG_VERSION);
        }
    }

    /**
     * 添加内容哈希字段
     */
    private function add_content_hash_column() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'esig_contract_templates';
        
        // 检查字段是否已存在
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'content_hash'");
        
        if (empty($column_exists)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN content_hash varchar(32) AFTER variables");
            $wpdb->query("ALTER TABLE $table_name ADD INDEX content_hash (content_hash)");
        }
    }

    /**
     * 智能同步默认模板（已禁用 - 避免覆盖用户自定义内容）
     */
    public function sync_default_template() {
        // 功能已禁用，避免自动覆盖用户在后台手动修改的合同模板内容
        error_log('WP Electronic Signature: 自动同步功能已禁用，保护用户自定义内容');
        return;

        // 以下代码已禁用
        /*
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';
        $current_hash = $this->get_default_template_hash();
        $current_content = $this->get_default_contract_template();

        // 检查数据库中是否有与当前代码匹配的模板
        $existing_template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE content_hash = %s AND is_default = 1",
            $current_hash
        ));

        if (!$existing_template) {
            // 数据库中的默认模板与代码不一致，需要更新
            $wpdb->query($wpdb->prepare(
                "UPDATE $table_name SET
                    content = %s,
                    content_hash = %s,
                    updated_at = NOW()
                WHERE is_default = 1",
                $current_content,
                $current_hash
            ));

            error_log('WP Electronic Signature: 默认模板已自动同步到最新版本');
        }
        */
    }

    /**
     * 检查并自动同步模板（已禁用 - 避免覆盖用户自定义内容）
     */
    public function auto_sync_template_if_needed() {
        // 功能已禁用，避免自动覆盖用户在后台手动修改的合同模板内容
        error_log('WP Electronic Signature: 自动同步检查功能已禁用，保护用户自定义内容');
        return;

        // 以下代码已禁用
        /*
        // 为了性能，使用缓存机制，每小时最多检查一次
        $last_sync_check = get_transient('wp_esig_last_sync_check');
        if ($last_sync_check) {
            return;
        }

        $this->sync_default_template();
        set_transient('wp_esig_last_sync_check', time(), HOUR_IN_SECONDS);
        */
    }

    /**
     * 更新插件（已禁用自动模板更新功能）
     */
    private function update_plugin($old_version) {
        // 自动模板更新功能已禁用，避免覆盖用户自定义内容
        // 只保留必要的配置更新

        // 如果是从2.2.3之前的版本更新，强制设置为页面加载时弹窗签名
        if (version_compare($old_version, '2.2.3', '<')) {
            update_option('wp_esig_signature_timing', 'on_page_load');
        }

        // 如果是从2.4.4之前的版本更新，确保数据库表存在（但不强制更新模板内容）
        if (version_compare($old_version, '2.4.4', '<')) {
            // 只确保表结构存在，不更新模板内容
            $this->create_tables();
        }

        // 如果是从2.5.1之前的版本更新，添加content_hash字段（但不同步模板）
        if (version_compare($old_version, '2.5.1', '<')) {
            $this->add_content_hash_column();
            // 移除自动同步：$this->sync_default_template();
        }

        // 如果是从2.5.1版本更新，确保所有模板都有正确的content_hash
        if (version_compare($old_version, '2.5.2', '<')) {
            $this->update_all_template_hashes();
        }

        // 以下自动模板更新功能已禁用，避免覆盖用户自定义内容：
        // - $this->update_contract_templates();
        // - $this->force_update_contract_templates();
        // - $this->sync_default_template();
        // 如需更新模板，请在后台手动操作
    }

    /**
     * 更新合同模板
     */
    private function update_contract_templates() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';
        $default_content = $this->get_default_contract_template();

        // 更新所有默认模板
        $wpdb->update(
            $table_name,
            array(
                'content' => $default_content,
                'content_hash' => $this->get_default_template_hash(),
                'updated_at' => current_time('mysql')
            ),
            array('is_default' => 1),
            array('%s', '%s', '%s'),
            array('%d')
        );

        // 如果没有默认模板，创建一个
        $existing = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE is_default = 1");
        if ($existing == 0) {
            $this->create_default_template();
        }
    }

    /**
     * 更新所有模板的content_hash
     */
    private function update_all_template_hashes() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';

        // 获取所有模板
        $templates = $wpdb->get_results("SELECT id, content FROM $table_name WHERE status = 'active'");

        foreach ($templates as $template) {
            $content_hash = md5($template->content);
            $wpdb->update(
                $table_name,
                array('content_hash' => $content_hash),
                array('id' => $template->id),
                array('%s'),
                array('%d')
            );
        }

        error_log('WP Electronic Signature: 已更新所有模板的content_hash');
    }

    /**
     * 强制更新合同模板（用于修复签名区域显示问题）
     */
    private function force_update_contract_templates() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'esig_contract_templates';
        $default_content = $this->get_default_contract_template();

        // 删除所有现有模板
        $wpdb->query("DELETE FROM $table_name");

        // 重新创建默认模板
        $wpdb->insert(
            $table_name,
            array(
                'name' => __('默认合同模板', 'wp-electronic-signature'),
                'content' => $default_content,
                'content_hash' => $this->get_default_template_hash(),
                'is_default' => 1,
                'status' => 'active',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s')
        );

        // 记录日志
        error_log('WP Electronic Signature: 强制更新合同模板完成 - 版本 2.2.5');
    }

    /**
     * WooCommerce缺失提醒
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('WP Electronic Signature 需要 WooCommerce 插件才能正常工作。请先安装并激活 WooCommerce。', 'wp-electronic-signature');
        echo '</p></div>';
    }
}

// 初始化插件
function wp_electronic_signature() {
    return WP_Electronic_Signature::get_instance();
}

// 启动插件
wp_electronic_signature();